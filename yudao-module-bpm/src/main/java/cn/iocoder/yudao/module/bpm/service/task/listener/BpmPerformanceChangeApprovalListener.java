package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEventListener;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;

/**
 * 业绩归属变更审批通过后更新合同表单审批的监听器
 * 监听流程：{@value #PERFORMANCE_CHANGE_PROCESS_KEY} (业绩归属变更)
 * 目标流程：{@value #CONTRACT_APPROVAL_PROCESS_KEY} (合同表单审批)
 * 关联关系：通过 contractApprovalId 字段关联
 * - 业绩归属变更流程变量中的 contractApprovalId 实际上是合同审批流程的 processInstanceId
 * - 直接通过这个 processInstanceId 来查找和更新对应的合同审批流程
 *
 * 处理逻辑：当业绩归属变更审批通过时，将其 item 字段更新到关联的合同表单审批中
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BpmPerformanceChangeApprovalListener extends BpmProcessInstanceStatusEventListener {

    /**
     * 业绩归属变更流程定义Key
     */
    private static final String PERFORMANCE_CHANGE_PROCESS_KEY = "_Test_4";

    /**
     * 合同表单审批流程定义Key
     */
    private static final String CONTRACT_APPROVAL_PROCESS_KEY = "_test_4";

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private HistoryService historyService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    protected String getProcessDefinitionKey() {
        return PERFORMANCE_CHANGE_PROCESS_KEY; // 监听业绩归属变更流程
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        // 只处理审批通过的情况
        if (!BpmTaskStatusEnum.APPROVE.getStatus().equals(event.getStatus())) {
            log.debug("[onEvent][业绩归属变更流程未通过，跳过处理] processInstanceId={}, status={}", 
                    event.getId(), event.getStatus());
            return;
        }

        log.info("[onEvent][业绩归属变更流程审批通过，开始处理] processInstanceId={}", event.getId());

        try {
            // 1. 获取业绩归属变更流程的变量
            Map<String, Object> variables = getProcessVariables(event.getId());
            if (variables == null || variables.isEmpty()) {
                log.warn("[onEvent][获取流程变量失败] processInstanceId={}", event.getId());
                return;
            }

            // 2. 获取关联的合同审批ID（业务Key）
            String contractApprovalId = getContractApprovalId(variables);
            if (StrUtil.isEmpty(contractApprovalId)) {
                return;
            }

            // 3. 获取业绩归属变更的item数据
            Object itemData = variables.get("item");
            if (itemData == null) {
                return;
            }

            // 4. 查找关联的合同表单审批流程实例
            String contractProcessInstanceId = findContractProcessInstance(contractApprovalId);
            if (StrUtil.isEmpty(contractProcessInstanceId)) {
                return;
            }

            // 5. 更新合同审批流程的item变量
            updateContractProcessItem(contractProcessInstanceId, itemData);
            
            log.info("[onEvent][业绩归属变更处理完成] processInstanceId={}, contractProcessInstanceId={}", 
                    event.getId(), contractProcessInstanceId);

        } catch (Exception e) {
            log.error("[onEvent][处理业绩归属变更失败] processInstanceId={}", event.getId(), e);
            // 不抛出异常，避免影响工作流状态更新
        }
    }

    /**
     * 获取流程变量
     */
    private Map<String, Object> getProcessVariables(String processInstanceId) {
        try {
            // 先尝试从运行中的流程实例获取
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            
            if (processInstance != null) {
                return runtimeService.getVariables(processInstanceId);
            }
            
            // 如果流程已结束，从历史中获取
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            
            if (historicProcessInstance != null) {
                List<HistoricVariableInstance> historicVariables = historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .list();
                
                Map<String, Object> variables = new java.util.HashMap<>();
                for (HistoricVariableInstance variable : historicVariables) {
                    variables.put(variable.getVariableName(), variable.getValue());
                }
                return variables;
            }
            
            return null;
        } catch (Exception e) {
            log.error("[getProcessVariables][获取流程变量失败] processInstanceId={}", processInstanceId, e);
            return null;
        }
    }

    /**
     * 从变量中获取关联的合同审批流程实例ID
     */
    private String getContractApprovalId(Map<String, Object> variables) {
        // 直接获取contractApprovalId字段，这实际上是合同审批流程的processInstanceId
        Object contractApprovalId = variables.get("contractApprovalId");
        if (contractApprovalId != null) {
            return contractApprovalId.toString();
        }

        log.warn("[getContractApprovalId][未找到contractApprovalId字段] variables={}", variables.keySet());
        return null;
    }

    /**
     * 查找关联的合同审批流程实例
     */
    private String findContractProcessInstance(String contractApprovalId) {
        try {
            if (StrUtil.isEmpty(contractApprovalId)) {
                return null;
            }

            log.info("[findContractProcessInstance][查找合同审批流程] contractApprovalId={}", contractApprovalId);

            // 方法1：直接通过processInstanceId查找（contractApprovalId就是流程实例ID）
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(contractApprovalId)
                    .singleResult();

            if (processInstance != null && CONTRACT_APPROVAL_PROCESS_KEY.equals(processInstance.getProcessDefinitionKey())) {
                log.info("[findContractProcessInstance][通过processInstanceId找到运行中的合同审批流程] processInstanceId={}", contractApprovalId);
                return contractApprovalId;
            }

            // 方法2：如果运行中没有，检查历史流程
            HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(contractApprovalId)
                    .singleResult();

            if (historicInstance != null && CONTRACT_APPROVAL_PROCESS_KEY.equals(historicInstance.getProcessDefinitionKey())) {
                log.info("[findContractProcessInstance][通过processInstanceId找到历史合同审批流程] processInstanceId={}", contractApprovalId);
                return contractApprovalId;
            }

            // 方法3：如果上面都不匹配，尝试通过businessKey查找
            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery()
                    .processDefinitionKey(CONTRACT_APPROVAL_PROCESS_KEY)
                    .processInstanceBusinessKey(contractApprovalId)
                    .list();

            if (CollUtil.isNotEmpty(processInstances)) {
                String processInstanceId = processInstances.get(0).getProcessInstanceId();
                log.info("[findContractProcessInstance][通过businessKey找到运行中的合同审批流程] processInstanceId={}", processInstanceId);
                return processInstanceId;
            }

            // 方法4：通过流程变量查找
            processInstances = runtimeService.createProcessInstanceQuery()
                    .processDefinitionKey(CONTRACT_APPROVAL_PROCESS_KEY)
                    .variableValueEquals("contractApprovalId", contractApprovalId)
                    .list();

            if (CollUtil.isNotEmpty(processInstances)) {
                String processInstanceId = processInstances.get(0).getProcessInstanceId();
                log.info("[findContractProcessInstance][通过变量找到运行中的合同审批流程] processInstanceId={}", processInstanceId);
                return processInstanceId;
            }

            // 方法5：通过businessKey查找历史流程
            List<HistoricProcessInstance> historicInstances = historyService.createHistoricProcessInstanceQuery()
                    .processDefinitionKey(CONTRACT_APPROVAL_PROCESS_KEY)
                    .processInstanceBusinessKey(contractApprovalId)
                    .list();

            if (CollUtil.isNotEmpty(historicInstances)) {
                String processInstanceId = historicInstances.get(0).getId();
                log.info("[findContractProcessInstance][通过businessKey找到历史合同审批流程] processInstanceId={}", processInstanceId);
                return processInstanceId;
            }

            // 方法6：通过历史变量查找
            historicInstances = historyService.createHistoricProcessInstanceQuery()
                    .processDefinitionKey(CONTRACT_APPROVAL_PROCESS_KEY)
                    .variableValueEquals("contractApprovalId", contractApprovalId)
                    .list();

            if (CollUtil.isNotEmpty(historicInstances)) {
                String processInstanceId = historicInstances.get(0).getId();
                log.info("[findContractProcessInstance][通过变量找到历史合同审批流程] processInstanceId={}", processInstanceId);
                return processInstanceId;
            }

            log.warn("[findContractProcessInstance][未找到关联的合同审批流程] contractApprovalId={}", contractApprovalId);
            return null;
        } catch (Exception e) {
            log.error("[findContractProcessInstance][查找合同审批流程失败] contractApprovalId={}", contractApprovalId, e);
            return null;
        }
    }

    /**
     * 更新合同审批流程的item变量
     */
    private void updateContractProcessItem(String processInstanceId, Object itemData) {
        try {
            // 检查流程是否还在运行中
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance != null) {
                // 流程还在运行中，可以直接更新变量
                runtimeService.setVariable(processInstanceId, "item", itemData);
            } else {
                // 流程已结束，通过直接更新历史变量表来实现
                updateHistoricVariable(processInstanceId, "item", itemData);
            }
        } catch (Exception e) {
            log.error("[updateContractProcessItem][更新流程变量失败] processInstanceId={}", processInstanceId, e);
            throw e;
        }
    }

    /**
     * 更新历史流程变量
     */
    private void updateHistoricVariable(String processInstanceId, String variableName, Object value) {
        try {
            // 将对象转换为JSON字符串存储
            String jsonValue = null;
            if (value != null) {
                if (value instanceof String) {
                    jsonValue = (String) value;
                } else {
                    try {
                        // 使用Jackson将对象转换为JSON字符串
                        jsonValue = objectMapper.writeValueAsString(value);
                    } catch (Exception jsonEx) {
                        log.warn("[updateHistoricVariable][JSON序列化失败，使用toString] value={}", value, jsonEx);
                        jsonValue = value.toString();
                    }
                }
            }

            // 检查历史变量是否已存在
            List<HistoricVariableInstance> existingVars = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .variableName(variableName)
                    .list();

            if (CollUtil.isNotEmpty(existingVars)) {
                // 更新现有的历史变量
                String sql = "UPDATE ACT_HI_VARINST SET TEXT_ = ?, LAST_UPDATED_TIME_ = NOW() WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
                int updated = jdbcTemplate.update(sql, jsonValue, processInstanceId, variableName);
                log.info("[updateHistoricVariable][更新历史变量] processInstanceId={}, variableName={}, updated={}",
                        processInstanceId, variableName, updated);
            } else {
                // 插入新的历史变量记录
                String sql = "INSERT INTO ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, VAR_TYPE_, REV_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_) " +
                           "VALUES (?, ?, ?, NULL, ?, 'string', 1, NULL, NULL, NULL, ?, NULL, NOW(), NOW())";

                // 生成一个简单的ID（实际应该用UUID或其他唯一ID生成策略）
                String varId = processInstanceId + "_" + variableName + "_" + System.currentTimeMillis();

                int inserted = jdbcTemplate.update(sql, varId, processInstanceId, processInstanceId, variableName, jsonValue);
                log.info("[updateHistoricVariable][插入历史变量] processInstanceId={}, variableName={}, inserted={}",
                        processInstanceId, variableName, inserted);
            }
        } catch (Exception e) {
            log.error("[updateHistoricVariable][更新历史变量失败] processInstanceId={}, variableName={}",
                    processInstanceId, variableName, e);
            throw e;
        }
    }
}
